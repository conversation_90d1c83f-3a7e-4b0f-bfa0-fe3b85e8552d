"use client"

import { useState, useEffect } from "react"
import { motion, AnimatePresence } from "framer-motion"
import { X, Globe, Mic, <PERSON>c<PERSON><PERSON>, Send, Share2, Download } from "lucide-react"
import WorldPulseOrb from "./WorldPulseOrb"
import SDGQuest from "./SDGQuest"
import LangSelector from "./LangSelector"
import NetworkFeed from "./NetworkFeed"
import { useHorizon } from "../contexts/HorizonContext"
import { useIntl } from "../contexts/IntlContext"

export default function HorizonHUD() {
  const { toggleHUD, addToNetwork, unityPoints, questsCompleted } = useHorizon()
  const { currentLang, translate } = useIntl()
  const [query, setQuery] = useState("")
  const [isListening, setIsListening] = useState(false)
  const [response, setResponse] = useState("")
  const [isLoading, setIsLoading] = useState(false)
  const [orbVariant, setOrbVariant] = useState<"idle" | "processing" | "unite">("idle")
  const [showNetworkFeed, setShowNetworkFeed] = useState(false)

  // Voice recognition setup
  useEffect(() => {
    if ("webkitSpeechRecognition" in window || "SpeechRecognition" in window) {
      const SpeechRecognition = (window as any).webkitSpeechRecognition || (window as any).SpeechRecognition
      const recognition = new SpeechRecognition()
      recognition.continuous = false
      recognition.interimResults = false
      recognition.lang = currentLang

      recognition.onresult = (event: any) => {
        const transcript = event.results[0][0].transcript
        setQuery(transcript)
        setIsListening(false)
      }

      recognition.onerror = () => {
        setIsListening(false)
      }

      if (isListening) {
        recognition.start()
      }

      return () => {
        recognition.stop()
      }
    }
  }, [isListening, currentLang])

  const handleVoiceToggle = () => {
    setIsListening(!isListening)
  }

  const handleSubmitQuery = async () => {
    if (!query.trim()) return

    setIsLoading(true)
    setOrbVariant("processing")

    try {
      // Mock API call to Grok
      const response = await fetch("/api/horizon-query", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          query,
          lang: currentLang,
          sdg: "general",
        }),
      })

      const data = await response.json()
      setResponse(data.response)
      setOrbVariant("unite")

      // Add to network feed
      addToNetwork({
        continent: data.continent || "Global",
        tip: query,
        points: data.points || 10,
        timestamp: Date.now(),
      })

      // Speak response if supported
      if ("speechSynthesis" in window) {
        const utterance = new SpeechSynthesisUtterance(data.response)
        utterance.lang = currentLang
        speechSynthesis.speak(utterance)
      }
    } catch (error) {
      console.error("Query failed:", error)
      setResponse(translate("query.error"))
    } finally {
      setIsLoading(false)
      setTimeout(() => setOrbVariant("idle"), 3000)
    }
  }

  const handleExportGIF = async () => {
    // Mock GIF export functionality
    const canvas = document.createElement("canvas")
    const ctx = canvas.getContext("2d")
    if (ctx) {
      canvas.width = 400
      canvas.height = 400
      ctx.fillStyle = "#10b981"
      ctx.fillRect(0, 0, 400, 400)
      ctx.fillStyle = "#ffffff"
      ctx.font = "20px Inter"
      ctx.textAlign = "center"
      ctx.fillText("Grok Horizon", 200, 200)
      ctx.fillText("#SDGUnited", 200, 230)

      canvas.toBlob((blob) => {
        if (blob) {
          const url = URL.createObjectURL(blob)
          const a = document.createElement("a")
          a.href = url
          a.download = "horizon-impact.gif"
          a.click()
          URL.revokeObjectURL(url)
        }
      })
    }
  }

  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
      className="fixed inset-0 z-50 bg-black/50 backdrop-blur-sm"
      onClick={(e) => e.target === e.currentTarget && toggleHUD()}
    >
      <div className="absolute inset-4 md:inset-8 glassmorphism rounded-2xl overflow-hidden">
        {/* Header */}
        <div className="flex items-center justify-between p-4 border-b border-white/10">
          <div className="flex items-center gap-4">
            <Globe className="w-6 h-6 text-horizon-green" />
            <h2 className="text-xl font-space-grotesk font-bold">{translate("hud.title")}</h2>
            <div className="flex items-center gap-2 text-sm text-slate-400">
              <span>{unityPoints} Unity Points</span>
              <span>•</span>
              <span>{questsCompleted} Quests</span>
            </div>
          </div>

          <div className="flex items-center gap-2">
            <LangSelector />
            <button
              onClick={() => setShowNetworkFeed(!showNetworkFeed)}
              className="p-2 hover:bg-white/10 rounded-lg transition-colors"
              aria-label="Toggle network feed"
            >
              <Share2 className="w-5 h-5" />
            </button>
            <button
              onClick={toggleHUD}
              className="p-2 hover:bg-white/10 rounded-lg transition-colors"
              aria-label="Close HUD"
            >
              <X className="w-5 h-5" />
            </button>
          </div>
        </div>

        <div className="flex h-[calc(100%-80px)]">
          {/* Main Content */}
          <div className="flex-1 flex flex-col">
            {/* World Pulse Orb */}
            <div className="flex-1 flex items-center justify-center p-8">
              <div className="relative">
                <WorldPulseOrb variant={orbVariant} />

                {/* Response overlay */}
                <AnimatePresence>
                  {response && (
                    <motion.div
                      initial={{ opacity: 0, scale: 0.8 }}
                      animate={{ opacity: 1, scale: 1 }}
                      exit={{ opacity: 0, scale: 0.8 }}
                      className="absolute -bottom-20 left-1/2 transform -translate-x-1/2 w-80 p-4 glassmorphism rounded-lg text-center"
                    >
                      <p className="text-sm text-slate-200">{response}</p>
                      <button
                        onClick={handleExportGIF}
                        className="mt-2 px-3 py-1 bg-horizon-green/20 hover:bg-horizon-green/30 rounded text-xs transition-colors"
                      >
                        <Download className="w-3 h-3 inline mr-1" />
                        Export Impact
                      </button>
                    </motion.div>
                  )}
                </AnimatePresence>
              </div>
            </div>

            {/* Query Input */}
            <div className="p-6 border-t border-white/10">
              <div className="flex gap-3 mb-4">
                <div className="flex-1 relative">
                  <input
                    type="text"
                    value={query}
                    onChange={(e) => setQuery(e.target.value)}
                    onKeyPress={(e) => e.key === "Enter" && handleSubmitQuery()}
                    placeholder={translate("query.placeholder")}
                    className="w-full px-4 py-3 bg-slate-800/50 border border-white/20 rounded-lg focus:outline-none focus:border-horizon-green text-white placeholder-slate-400"
                    disabled={isLoading}
                  />
                  {isListening && (
                    <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
                      <div className="w-2 h-2 bg-red-500 rounded-full animate-pulse" />
                    </div>
                  )}
                </div>

                <button
                  onClick={handleVoiceToggle}
                  className={`p-3 rounded-lg transition-colors ${
                    isListening ? "bg-red-500/20 text-red-400" : "bg-slate-700/50 hover:bg-slate-700 text-slate-300"
                  }`}
                  aria-label={isListening ? "Stop listening" : "Start voice input"}
                >
                  {isListening ? <MicOff className="w-5 h-5" /> : <Mic className="w-5 h-5" />}
                </button>

                <button
                  onClick={handleSubmitQuery}
                  disabled={!query.trim() || isLoading}
                  className="px-6 py-3 bg-horizon-green hover:bg-horizon-green/80 disabled:bg-slate-700 disabled:text-slate-500 rounded-lg transition-colors font-medium"
                >
                  {isLoading ? (
                    <div className="w-5 h-5 border-2 border-white/30 border-t-white rounded-full animate-spin" />
                  ) : (
                    <Send className="w-5 h-5" />
                  )}
                </button>
              </div>

              {/* SDG Quest Buttons */}
              <SDGQuest onQuestSelect={(quest) => setQuery(quest)} />
            </div>
          </div>

          {/* Network Feed Sidebar */}
          <AnimatePresence>
            {showNetworkFeed && (
              <motion.div
                initial={{ width: 0, opacity: 0 }}
                animate={{ width: 320, opacity: 1 }}
                exit={{ width: 0, opacity: 0 }}
                className="border-l border-white/10 overflow-hidden"
              >
                <NetworkFeed />
              </motion.div>
            )}
          </AnimatePresence>
        </div>
      </div>
    </motion.div>
  )
}
