"use client"

import { motion } from "framer-motion"
import { Globe, TrendingUp, Users, Award } from "lucide-react"
import { useHorizon } from "../contexts/HorizonContext"
import { useIntl } from "../contexts/IntlContext"

export default function NetworkFeed() {
  const { networkFeed, unityPoints } = useHorizon()
  const { translate } = useIntl()

  const continentColors: Record<string, string> = {
    Africa: "text-orange-400",
    Asia: "text-red-400",
    Europe: "text-blue-400",
    "North America": "text-green-400",
    "South America": "text-yellow-400",
    Oceania: "text-purple-400",
    Global: "text-cyan-400",
  }

  const continentFlags: Record<string, string> = {
    Africa: "🌍",
    Asia: "🌏",
    Europe: "🌍",
    "North America": "🌎",
    "South America": "🌎",
    Oceania: "🌏",
    Global: "🌐",
  }

  return (
    <div className="h-full flex flex-col">
      {/* Header */}
      <div className="p-4 border-b border-white/10">
        <div className="flex items-center gap-2 mb-3">
          <Globe className="w-5 h-5 text-horizon-green" />
          <h3 className="font-semibold">{translate("network.title")}</h3>
        </div>

        {/* Global Stats */}
        <div className="grid grid-cols-2 gap-3 text-xs">
          <div className="bg-slate-800/50 rounded-lg p-2 text-center">
            <Users className="w-4 h-4 mx-auto mb-1 text-blue-400" />
            <div className="font-semibold text-blue-400">47.2K</div>
            <div className="text-slate-400">Active Voices</div>
          </div>
          <div className="bg-slate-800/50 rounded-lg p-2 text-center">
            <TrendingUp className="w-4 h-4 mx-auto mb-1 text-green-400" />
            <div className="font-semibold text-green-400">+23%</div>
            <div className="text-slate-400">Impact Growth</div>
          </div>
        </div>
      </div>

      {/* Feed */}
      <div className="flex-1 overflow-y-auto p-4 space-y-3">
        {networkFeed.length === 0 ? (
          <div className="text-center text-slate-400 py-8">
            <Globe className="w-8 h-8 mx-auto mb-2 opacity-50" />
            <p className="text-sm">{translate("network.empty")}</p>
          </div>
        ) : (
          networkFeed.map((item, index) => (
            <motion.div
              key={`${item.timestamp}-${index}`}
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: index * 0.1 }}
              className="bg-slate-800/30 rounded-lg p-3 border border-white/5"
            >
              <div className="flex items-start gap-2 mb-2">
                <span className="text-lg">{continentFlags[item.continent] || "🌐"}</span>
                <div className="flex-1 min-w-0">
                  <div className="flex items-center gap-2 mb-1">
                    <span className={`text-xs font-medium ${continentColors[item.continent] || "text-slate-400"}`}>
                      {item.continent}
                    </span>
                    <span className="text-xs text-slate-500">{new Date(item.timestamp).toLocaleTimeString()}</span>
                  </div>
                  <p className="text-sm text-slate-200 line-clamp-2">{item.tip}</p>
                  <div className="flex items-center gap-2 mt-2">
                    <Award className="w-3 h-3 text-horizon-green" />
                    <span className="text-xs text-horizon-green">+{item.points} Unity Points</span>
                  </div>
                </div>
              </div>
            </motion.div>
          ))
        )}

        {/* Mock Global Insights */}
        <div className="border-t border-white/10 pt-4 mt-4">
          <h4 className="text-sm font-medium text-slate-300 mb-3">Global Insights</h4>
          <div className="space-y-2">
            {[
              { continent: "Asia", insight: "Solar panel cooperatives gaining momentum", trend: "+15%" },
              { continent: "Africa", insight: "Mobile health clinics expanding access", trend: "+28%" },
              { continent: "Europe", insight: "Circular economy initiatives thriving", trend: "+12%" },
            ].map((insight, index) => (
              <div key={index} className="bg-slate-800/20 rounded p-2">
                <div className="flex items-center justify-between mb-1">
                  <span className={`text-xs font-medium ${continentColors[insight.continent]}`}>
                    {continentFlags[insight.continent]} {insight.continent}
                  </span>
                  <span className="text-xs text-green-400">{insight.trend}</span>
                </div>
                <p className="text-xs text-slate-300">{insight.insight}</p>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Footer */}
      <div className="p-4 border-t border-white/10">
        <div className="text-center">
          <div className="text-lg font-bold text-horizon-green">{unityPoints}</div>
          <div className="text-xs text-slate-400">Your Unity Points</div>
          <div className="mt-2 h-1 bg-slate-700 rounded-full overflow-hidden">
            <div
              className="h-full bg-gradient-to-r from-horizon-blue to-horizon-green transition-all duration-1000"
              style={{ width: `${Math.min((unityPoints / 1000) * 100, 100)}%` }}
            />
          </div>
          <p className="text-xs text-slate-500 mt-1">
            {1000 - unityPoints > 0 ? `${1000 - unityPoints} points to next level` : "Unity Master!"}
          </p>
        </div>
      </div>
    </div>
  )
}
