"use client"

import { createContext, useContext, useState, useEffect, type ReactNode } from "react"

interface NetworkFeedItem {
  continent: string
  tip: string
  points: number
  timestamp: number
}

interface HorizonContextType {
  isHUDVisible: boolean
  toggleHUD: () => void
  isFirstVisit: boolean
  setIsFirstVisit: (value: boolean) => void
  networkFeed: NetworkFeedItem[]
  addToNetwork: (item: NetworkFeedItem) => void
  unityPoints: number
  questsCompleted: number
  addUnityPoints: (points: number) => void
}

const HorizonContext = createContext<HorizonContextType | undefined>(undefined)

export function HorizonProvider({ children }: { children: ReactNode }) {
  const [isHUDVisible, setIsHUDVisible] = useState(false)
  const [isFirstVisit, setIsFirstVisit] = useState(true)
  const [networkFeed, setNetworkFeed] = useState<NetworkFeedItem[]>([])
  const [unityPoints, setUnityPoints] = useState(0)
  const [questsCompleted, setQuestsCompleted] = useState(0)

  // Load saved data on mount
  useEffect(() => {
    const savedFirstVisit = localStorage.getItem("horizon-first-visit")
    const savedPoints = localStorage.getItem("horizon-unity-points")
    const savedQuests = localStorage.getItem("horizon-quests-completed")
    const savedFeed = localStorage.getItem("horizon-network-feed")

    if (savedFirstVisit !== null) {
      setIsFirstVisit(savedFirstVisit === "true")
    }
    if (savedPoints) {
      setUnityPoints(Number.parseInt(savedPoints, 10))
    }
    if (savedQuests) {
      setQuestsCompleted(Number.parseInt(savedQuests, 10))
    }
    if (savedFeed) {
      try {
        setNetworkFeed(JSON.parse(savedFeed))
      } catch (error) {
        console.error("Failed to parse saved network feed:", error)
      }
    }
  }, [])

  // Save data when it changes
  useEffect(() => {
    localStorage.setItem("horizon-first-visit", isFirstVisit.toString())
  }, [isFirstVisit])

  useEffect(() => {
    localStorage.setItem("horizon-unity-points", unityPoints.toString())
  }, [unityPoints])

  useEffect(() => {
    localStorage.setItem("horizon-quests-completed", questsCompleted.toString())
  }, [questsCompleted])

  useEffect(() => {
    localStorage.setItem("horizon-network-feed", JSON.stringify(networkFeed))
  }, [networkFeed])

  const toggleHUD = () => {
    setIsHUDVisible(!isHUDVisible)
  }

  const addToNetwork = (item: NetworkFeedItem) => {
    setNetworkFeed((prev) => [item, ...prev].slice(0, 50)) // Keep last 50 items
    addUnityPoints(item.points)
    setQuestsCompleted((prev) => prev + 1)
  }

  const addUnityPoints = (points: number) => {
    setUnityPoints((prev) => prev + points)
  }

  return (
    <HorizonContext.Provider
      value={{
        isHUDVisible,
        toggleHUD,
        isFirstVisit,
        setIsFirstVisit,
        networkFeed,
        addToNetwork,
        unityPoints,
        questsCompleted,
        addUnityPoints,
      }}
    >
      {children}
    </HorizonContext.Provider>
  )
}

export function useHorizon() {
  const context = useContext(HorizonContext)
  if (context === undefined) {
    throw new Error("useHorizon must be used within a HorizonProvider")
  }
  return context
}
