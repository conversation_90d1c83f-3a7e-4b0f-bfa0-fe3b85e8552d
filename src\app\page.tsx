"use client"

import { useEffect, useState } from "react"
import { motion, AnimatePresence } from "framer-motion"
import HorizonHUD from "../components/HorizonHUD"
import WorldPulseOrb from "../components/WorldPulseOrb"
import WelcomeModal from "../components/WelcomeModal"
import { useHorizon } from "../contexts/HorizonContext"

export default function Home() {
  const { isHUDVisible, toggleHUD, isFirstVisit, setIsFirstVisit } = useHorizon()
  const [showWelcome, setShowWelcome] = useState(false)

  useEffect(() => {
    // Global keyboard listener for H key toggle
    const handleKeyPress = (event: KeyboardEvent) => {
      if (event.key.toLowerCase() === "h" && !event.ctrlKey && !event.metaKey) {
        event.preventDefault()
        toggleHUD()
      }
      if (event.key === "Escape" && isHUDVisible) {
        event.preventDefault()
        toggleHUD()
      }
    }

    window.addEventListener("keydown", handleKeyPress)

    // Show welcome modal on first visit
    if (isFirstVisit) {
      setShowWelcome(true)
    }

    return () => {
      window.removeEventListener("keydown", handleKeyPress)
    }
  }, [isHUDVisible, toggleHUD, isFirstVisit])

  const handleWelcomeComplete = () => {
    setShowWelcome(false)
    setIsFirstVisit(false)
    toggleHUD() // Show HUD after welcome
  }

  return (
    <main className="min-h-screen bg-gradient-to-br from-slate-900 via-slate-800 to-slate-900 relative overflow-hidden">
      {/* Background particles */}
      <div className="absolute inset-0 opacity-30">
        <WorldPulseOrb variant="background" />
      </div>

      {/* Main content */}
      <div className="relative z-10 flex flex-col items-center justify-center min-h-screen p-8">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          className="text-center max-w-4xl mx-auto"
        >
          <h1 className="text-6xl md:text-8xl font-space-grotesk font-bold mb-6 text-balance">
            <span className="horizon-gradient bg-clip-text text-transparent">Green Horizon</span>
          </h1>

          <p className="text-xl md:text-2xl text-slate-300 mb-8 text-pretty">
            Harness collective intelligence to solve global challenges. Press{" "}
            <kbd className="px-2 py-1 bg-slate-700 rounded text-horizon-green font-mono">H</kbd> to begin your journey.
          </p>

          <motion.div
            className="flex flex-wrap justify-center gap-4 mb-12"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.5, duration: 0.6 }}
          >
            {["Climate Action", "Quality Education", "Global Health", "Clean Water", "Peace & Justice"].map(
              (sdg, index) => (
                <span key={sdg} className="px-4 py-2 glassmorphism rounded-full text-sm font-medium">
                  {sdg}
                </span>
              ),
            )}
          </motion.div>

          <motion.button
            onClick={toggleHUD}
            className="px-8 py-4 glassmorphism rounded-full font-semibold text-lg hover:bg-horizon-green/20 transition-all duration-300 focus-visible:focus-visible"
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.8, duration: 0.6 }}
          >
            Launch Horizon HUD
          </motion.button>
        </motion.div>

        {/* Floating action hint */}
        <motion.div
          className="absolute bottom-8 left-1/2 transform -translate-x-1/2"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 1.2, duration: 0.6 }}
        >
          <p className="text-slate-400 text-sm text-center">
            Press <kbd className="px-1 py-0.5 bg-slate-700 rounded text-xs">H</kbd> anywhere to toggle •
            <kbd className="px-1 py-0.5 bg-slate-700 rounded text-xs ml-1">ESC</kbd> to close
          </p>
        </motion.div>
      </div>

      {/* HUD Overlay */}
      <AnimatePresence>{isHUDVisible && <HorizonHUD />}</AnimatePresence>

      {/* Welcome Modal */}
      <AnimatePresence>{showWelcome && <WelcomeModal onComplete={handleWelcomeComplete} />}</AnimatePresence>
    </main>
  )
}
