"use client"

import { useCallback, useMemo } from "react"
import { motion } from "framer-motion"
import Particles from "react-particles"
import { loadSlim } from "tsparticles-slim"
import type { Engine, ISourceOptions } from "tsparticles-engine"

interface WorldPulseOrbProps {
  variant?: "idle" | "processing" | "unite" | "background"
  className?: string
}

export default function WorldPulseOrb({ variant = "idle", className = "" }: WorldPulseOrbProps) {
  const particlesInit = useCallback(async (engine: Engine) => {
    await loadSlim(engine)
  }, [])

  // Particle configurations for different states
  const particleOptions: ISourceOptions = useMemo(() => {
    const baseConfig = {
      background: {
        color: {
          value: "transparent",
        },
      },
      fpsLimit: 120,
      interactivity: {
        events: {
          onClick: {
            enable: true,
            mode: "push",
          },
          onHover: {
            enable: true,
            mode: "repulse",
          },
          resize: true,
        },
        modes: {
          push: {
            quantity: 4,
          },
          repulse: {
            distance: 200,
            duration: 0.4,
          },
        },
      },
      particles: {
        color: {
          value: variant === "unite" ? "#10b981" : variant === "processing" ? "#0ea5e9" : "#64748b",
        },
        links: {
          color: variant === "unite" ? "#10b981" : variant === "processing" ? "#0ea5e9" : "#475569",
          distance: 150,
          enable: true,
          opacity: variant === "background" ? 0.2 : 0.5,
          width: 1,
        },
        move: {
          direction: "none",
          enable: true,
          outModes: {
            default: "bounce",
          },
          random: false,
          speed: variant === "unite" ? 3 : variant === "processing" ? 2 : 1,
          straight: false,
        },
        number: {
          density: {
            enable: true,
            area: 800,
          },
          value: variant === "unite" ? 150 : variant === "processing" ? 100 : variant === "background" ? 50 : 80,
        },
        opacity: {
          value: variant === "background" ? 0.3 : 0.7,
        },
        shape: {
          type: "circle",
        },
        size: {
          value: { min: 1, max: variant === "unite" ? 4 : 3 },
        },
      },
      detectRetina: true,
    }

    // Add special effects for unite variant
    if (variant === "unite") {
      return {
        ...baseConfig,
        particles: {
          ...baseConfig.particles,
          move: {
            ...baseConfig.particles.move,
            attract: {
              enable: true,
              rotateX: 600,
              rotateY: 1200,
            },
          },
          rotate: {
            value: 0,
            random: true,
            direction: "clockwise",
            animation: {
              enable: true,
              speed: 5,
              sync: false,
            },
          },
        },
      }
    }

    return baseConfig
  }, [variant])

  const orbVariants = {
    idle: {
      scale: 1,
      rotate: 0,
      transition: { duration: 2, ease: "easeInOut" },
    },
    processing: {
      scale: [1, 1.1, 1],
      rotate: [0, 180, 360],
      transition: { duration: 2, repeat: Number.POSITIVE_INFINITY, ease: "linear" },
    },
    unite: {
      scale: [1, 1.2, 1],
      rotate: [0, 360],
      transition: { duration: 3, repeat: Number.POSITIVE_INFINITY, ease: "easeInOut" },
    },
    background: {
      scale: 0.8,
      opacity: 0.6,
      transition: { duration: 1 },
    },
  }

  return (
    <div className={`relative ${className}`}>
      {/* Particle System */}
      <motion.div variants={orbVariants} animate={variant} className="w-80 h-80 relative">
        <Particles
          id={`particles-${variant}`}
          init={particlesInit}
          options={particleOptions}
          className="w-full h-full"
        />

        {/* Central Orb */}
        <div className="absolute inset-0 flex items-center justify-center">
          <motion.div
            className={`w-20 h-20 rounded-full border-2 ${
              variant === "unite"
                ? "border-horizon-green bg-horizon-green/20 world-glow"
                : variant === "processing"
                  ? "border-horizon-blue bg-horizon-blue/20"
                  : "border-slate-500 bg-slate-500/20"
            } flex items-center justify-center`}
            animate={{
              boxShadow:
                variant === "unite"
                  ? [
                      "0 0 20px rgba(16, 185, 129, 0.5)",
                      "0 0 40px rgba(16, 185, 129, 0.8)",
                      "0 0 20px rgba(16, 185, 129, 0.5)",
                    ]
                  : variant === "processing"
                    ? [
                        "0 0 20px rgba(14, 165, 233, 0.5)",
                        "0 0 40px rgba(14, 165, 233, 0.8)",
                        "0 0 20px rgba(14, 165, 233, 0.5)",
                      ]
                    : undefined,
            }}
            transition={{ duration: 2, repeat: Number.POSITIVE_INFINITY }}
          >
            {/* World Map Icon */}
            <svg
              className={`w-8 h-8 ${
                variant === "unite"
                  ? "text-horizon-green"
                  : variant === "processing"
                    ? "text-horizon-blue"
                    : "text-slate-400"
              }`}
              fill="currentColor"
              viewBox="0 0 24 24"
            >
              <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zM11 19.93c-3.94-.49-7-3.85-7-7.93 0-.62.08-1.21.21-1.79L9 15v1c0 1.1.9 2 2 2v1.93zm6.9-2.54c-.26-.81-1-1.39-1.9-1.39h-1v-3c0-.55-.45-1-1-1H8v-2h2c.55 0 1-.45 1-1V7h2c1.1 0 2-.9 2-2v-.41c2.93 1.19 5 4.06 5 7.41 0 2.08-.8 3.97-2.1 5.39z" />
            </svg>
          </motion.div>
        </div>

        {/* Connection Lines for Unite variant */}
        {variant === "unite" && (
          <div className="absolute inset-0">
            {[...Array(6)].map((_, i) => (
              <motion.div
                key={i}
                className="absolute w-px bg-gradient-to-r from-transparent via-horizon-green to-transparent"
                style={{
                  height: "100%",
                  left: "50%",
                  transformOrigin: "center",
                  transform: `rotate(${i * 60}deg)`,
                }}
                animate={{
                  opacity: [0, 1, 0],
                  scaleY: [0, 1, 0],
                }}
                transition={{
                  duration: 2,
                  repeat: Number.POSITIVE_INFINITY,
                  delay: i * 0.2,
                }}
              />
            ))}
          </div>
        )}
      </motion.div>

      {/* Status Text */}
      {variant !== "background" && (
        <motion.div
          className="absolute -bottom-8 left-1/2 transform -translate-x-1/2 text-center"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 0.5 }}
        >
          <p className="text-sm text-slate-400">
            {variant === "idle" && "Ready to connect"}
            {variant === "processing" && "Processing global insights..."}
            {variant === "unite" && "Uniting voices worldwide"}
          </p>
        </motion.div>
      )}
    </div>
  )
}
