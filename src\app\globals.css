@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --font-inter: "Inter", sans-serif;
    --font-space-grotesk: "Space Grotesk", sans-serif;
  }

  * {
    @apply border-border;
  }

  body {
    @apply bg-slate-900 text-slate-100;
    font-feature-settings: "rlig" 1, "calt" 1;
  }
}

@layer components {
  .glassmorphism {
    @apply bg-gradient-to-br from-cyan-400/10 to-emerald-400/10 backdrop-blur-md border border-white/20;
  }

  .horizon-gradient {
    @apply bg-gradient-to-r from-horizon-blue via-teal-500 to-horizon-green;
  }

  .world-glow {
    box-shadow: 0 0 50px rgba(16, 185, 129, 0.3), 0 0 100px rgba(14, 165, 233, 0.2);
  }

  .unity-pulse {
    animation: unity-pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
  }

  @keyframes unity-pulse {
    0%,
    100% {
      opacity: 1;
      transform: scale(1);
    }
    50% {
      opacity: 0.8;
      transform: scale(1.05);
    }
  }
}

@layer utilities {
  .text-balance {
    text-wrap: balance;
  }

  .text-pretty {
    text-wrap: pretty;
  }
}

/* Accessibility improvements */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* High contrast mode */
@media (prefers-contrast: high) {
  .glassmorphism {
    @apply bg-slate-800/90 border-white/50;
  }
}

/* Focus styles for accessibility */
.focus-visible:focus-visible {
  @apply outline-2 outline-offset-2 outline-horizon-green;
}
