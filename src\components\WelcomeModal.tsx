"use client"

import { useState } from "react"
import { motion, AnimatePresence } from "framer-motion"
import { Globe, Heart, Users, Zap, ArrowRight } from "lucide-react"

interface WelcomeModalProps {
  onComplete: () => void
}

const WELCOME_STEPS = [
  {
    icon: Globe,
    title: "Welcome to Grok Horizon",
    description: "A revolutionary HUD that transforms AI interactions into catalysts for planetary progress.",
    color: "text-horizon-green",
  },
  {
    icon: Users,
    title: "Join the Global Network",
    description: "Connect with voices worldwide to solve challenges aligned with UN Sustainable Development Goals.",
    color: "text-blue-400",
  },
  {
    icon: Heart,
    title: "Foster Unity",
    description:
      "Every query contributes to collective intelligence, turning individual insights into global solutions.",
    color: "text-red-400",
  },
  {
    icon: Zap,
    title: "Ready to Begin?",
    description: "Press H anywhere to toggle the HUD, or ESC to close. Your journey toward global impact starts now.",
    color: "text-yellow-400",
  },
]

export default function WelcomeModal({ onComplete }: WelcomeModalProps) {
  const [currentStep, setCurrentStep] = useState(0)
  const [optedIn, setOptedIn] = useState(false)

  const handleNext = () => {
    if (currentStep < WELCOME_STEPS.length - 1) {
      setCurrentStep(currentStep + 1)
    } else {
      onComplete()
    }
  }

  const handleOptIn = () => {
    setOptedIn(true)
    // Store opt-in preference
    localStorage.setItem("horizon-opted-in", "true")
  }

  const currentStepData = WELCOME_STEPS[currentStep]
  const Icon = currentStepData.icon

  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
      className="fixed inset-0 z-50 bg-black/70 backdrop-blur-sm flex items-center justify-center p-4"
    >
      <motion.div
        initial={{ scale: 0.8, opacity: 0 }}
        animate={{ scale: 1, opacity: 1 }}
        exit={{ scale: 0.8, opacity: 0 }}
        className="bg-slate-800 border border-white/20 rounded-2xl p-8 max-w-md w-full glassmorphism"
      >
        {/* Progress Indicator */}
        <div className="flex gap-2 mb-6">
          {WELCOME_STEPS.map((_, index) => (
            <div
              key={index}
              className={`h-1 flex-1 rounded-full transition-colors ${
                index <= currentStep ? "bg-horizon-green" : "bg-slate-600"
              }`}
            />
          ))}
        </div>

        {/* Content */}
        <AnimatePresence mode="wait">
          <motion.div
            key={currentStep}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            className="text-center"
          >
            <div
              className={`w-16 h-16 mx-auto mb-4 rounded-full bg-slate-700/50 flex items-center justify-center ${currentStepData.color}`}
            >
              <Icon className="w-8 h-8" />
            </div>

            <h2 className="text-2xl font-space-grotesk font-bold mb-4 text-balance">{currentStepData.title}</h2>

            <p className="text-slate-300 mb-6 text-pretty leading-relaxed">{currentStepData.description}</p>

            {/* Opt-in for data sharing (step 1) */}
            {currentStep === 1 && !optedIn && (
              <div className="mb-6 p-4 bg-slate-700/30 rounded-lg border border-white/10">
                <h3 className="text-sm font-medium mb-2">Join the Horizon Network?</h3>
                <p className="text-xs text-slate-400 mb-3">
                  Share anonymized insights to help build collective intelligence (optional)
                </p>
                <button
                  onClick={handleOptIn}
                  className="px-4 py-2 bg-horizon-green hover:bg-horizon-green/80 rounded-lg text-sm font-medium transition-colors"
                >
                  Yes, I want to contribute
                </button>
              </div>
            )}

            {/* Action Button */}
            <button
              onClick={handleNext}
              className="w-full px-6 py-3 bg-horizon-green hover:bg-horizon-green/80 rounded-lg font-semibold transition-colors flex items-center justify-center gap-2"
            >
              {currentStep < WELCOME_STEPS.length - 1 ? (
                <>
                  Continue
                  <ArrowRight className="w-4 h-4" />
                </>
              ) : (
                "Launch Horizon HUD"
              )}
            </button>

            {/* Skip option */}
            {currentStep < WELCOME_STEPS.length - 1 && (
              <button
                onClick={onComplete}
                className="mt-3 text-sm text-slate-400 hover:text-slate-300 transition-colors"
              >
                Skip introduction
              </button>
            )}
          </motion.div>
        </AnimatePresence>

        {/* Footer */}
        <div className="mt-6 pt-4 border-t border-white/10 text-center">
          <p className="text-xs text-slate-500">Privacy-first • Carbon-conscious • Built for humanity</p>
        </div>
      </motion.div>
    </motion.div>
  )
}
