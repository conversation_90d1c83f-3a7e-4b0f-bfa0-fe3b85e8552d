"use client"

import { useState } from "react"
import { motion } from "framer-motion"
import { Leaf, Heart, GraduationCap, Droplets, Scale, Zap } from "lucide-react"
import { useIntl } from "../contexts/IntlContext"

interface SDGQuestProps {
  onQuestSelect: (quest: string) => void
}

const SDG_QUESTS = [
  {
    id: "climate",
    icon: Leaf,
    title: "Climate Horizon",
    color: "text-green-400",
    bgColor: "bg-green-400/10 hover:bg-green-400/20",
    query: "How can my community reduce carbon emissions by 30% this year?",
  },
  {
    id: "health",
    icon: Heart,
    title: "Health Nexus",
    color: "text-red-400",
    bgColor: "bg-red-400/10 hover:bg-red-400/20",
    query: "What are the most effective preventive health measures for my region?",
  },
  {
    id: "education",
    icon: GraduationCap,
    title: "Education Bridge",
    color: "text-blue-400",
    bgColor: "bg-blue-400/10 hover:bg-blue-400/20",
    query: "How can we improve digital literacy in underserved communities?",
  },
  {
    id: "water",
    icon: Droplets,
    title: "Water Unity",
    color: "text-cyan-400",
    bgColor: "bg-cyan-400/10 hover:bg-cyan-400/20",
    query: "What are innovative water conservation techniques for urban areas?",
  },
  {
    id: "justice",
    icon: Scale,
    title: "Justice Compass",
    color: "text-purple-400",
    bgColor: "bg-purple-400/10 hover:bg-purple-400/20",
    query: "How can technology promote transparency in local governance?",
  },
  {
    id: "energy",
    icon: Zap,
    title: "Energy Flow",
    color: "text-yellow-400",
    bgColor: "bg-yellow-400/10 hover:bg-yellow-400/20",
    query: "What renewable energy solutions work best for small communities?",
  },
]

export default function SDGQuest({ onQuestSelect }: SDGQuestProps) {
  const { translate } = useIntl()
  const [selectedQuest, setSelectedQuest] = useState<string | null>(null)

  const handleQuestClick = (quest: (typeof SDG_QUESTS)[0]) => {
    setSelectedQuest(quest.id)
    onQuestSelect(quest.query)

    // Reset selection after animation
    setTimeout(() => setSelectedQuest(null), 1000)
  }

  return (
    <div className="space-y-3">
      <h3 className="text-sm font-medium text-slate-300 mb-3">{translate("sdg.title")} - Quick Start Quests</h3>

      <div className="grid grid-cols-2 md:grid-cols-3 gap-2">
        {SDG_QUESTS.map((quest) => {
          const Icon = quest.icon
          const isSelected = selectedQuest === quest.id

          return (
            <motion.button
              key={quest.id}
              onClick={() => handleQuestClick(quest)}
              className={`p-3 rounded-lg border border-white/10 transition-all duration-200 text-left ${quest.bgColor} ${
                isSelected ? "scale-95 opacity-75" : "hover:scale-105"
              }`}
              whileHover={{ y: -2 }}
              whileTap={{ scale: 0.95 }}
              disabled={isSelected}
            >
              <div className="flex items-center gap-2 mb-1">
                <Icon className={`w-4 h-4 ${quest.color}`} />
                <span className="text-sm font-medium text-white">{quest.title}</span>
              </div>

              <p className="text-xs text-slate-400 line-clamp-2">{quest.query}</p>

              {/* Progress indicator */}
              <div className="mt-2 h-1 bg-slate-700 rounded-full overflow-hidden">
                <motion.div
                  className={`h-full ${quest.color.replace("text-", "bg-")}`}
                  initial={{ width: "0%" }}
                  animate={{ width: isSelected ? "100%" : "0%" }}
                  transition={{ duration: 0.8 }}
                />
              </div>
            </motion.button>
          )
        })}
      </div>

      <p className="text-xs text-slate-500 text-center mt-3">
        {translate("sdg.subtitle")} • Fostering global unity through AI-powered insights
      </p>
    </div>
  )
}
