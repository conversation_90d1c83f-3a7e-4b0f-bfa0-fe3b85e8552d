# 🌍 Green Horizen - Global Collective Intelligence

A revolutionary HUD (Heads-Up Display) web app that harnesses Green AI to empower global collective intelligence and foster unity through AI-powered insights aligned with UN Sustainable Development Goals.

## ✨ Features

### 🎯 Core Capabilities
- **Toggleable HUD Overlay**: Press `H` anywhere to activate the digital horizon
- **World Pulse Orb**: Interactive particle visualization showing global connectivity
- **Multilingual Support**: Auto-detection and translation across 10+ languages
- **SDG Integration**: Quick-start quests for Climate, Health, Education, Water, Justice, and Energy
- **Global Network Feed**: Real-time insights from worldwide contributors
- **Unity Points System**: Gamified progress tracking for collective impact

### 🌐 Global Impact Features
- **Privacy-First**: No tracking, anonymized insights only
- **Offline Capable**: PWA with cached SDG primers for global accessibility
- **Carbon Conscious**: Eco-mode reduces animations on low battery
- **WCAG 3.0 Compliant**: Full accessibility with voice descriptions and high contrast
- **Export & Share**: Generate impact GIFs with #SDGUnited hashtags

### 🛠 Technical Stack
- **Next.js 14+** with App Router and TypeScript
- **Tailwind CSS** with glassmorphism and earth-inspired gradients
- **Framer Motion** for smooth animations and transitions
- **tsparticles** for the World Pulse Orb visualization
- **PWA Support** with next-pwa for offline functionality
- **Web APIs**: SpeechRecognition, SpeechSynthesis, Intl, Battery API

## 🚀 Quick Start

### Prerequisites
- Node.js 18+ 
- npm or yarn

### Installation
\`\`\`bash
# Clone the repository
git clone <repository-url>
cd green-horizen

# Install dependencies
npm install

# Start development server
npm run dev
\`\`\`

### Deployment to Vercel
\`\`\`bash
# Deploy to production
vercel --prod
\`\`\`

The app will be available at your Vercel domain, ready to help 7 billion people connect and solve global challenges.

## 🎮 Usage

1. **First Visit**: Welcome modal guides you through the Horizon Network opt-in
2. **Activate HUD**: Press `H` anywhere or click "Launch Horizon HUD"
3. **Query Global Challenges**: Type or speak your questions about SDGs
4. **Watch the Orb**: Particles flow between continents as your query processes
5. **Join the Network**: Share insights to contribute to collective intelligence
6. **Export Impact**: Generate shareable GIFs of your global contributions

### Keyboard Shortcuts
- `H` - Toggle HUD overlay
- `ESC` - Close HUD
- Voice input supported in 10+ languages

## 🌱 Global Impact Philosophy

Green Horizen transcends traditional AI interfaces by weaving artificial intelligence into the fabric of global solidarity. Every query becomes a quest that contributes to healing our world, one horizon at a time.

### UN SDG Alignment
- **SDG 4**: Quality Education through knowledge sharing
- **SDG 6**: Clean Water via conservation insights  
- **SDG 7**: Affordable Clean Energy through renewable solutions
- **SDG 13**: Climate Action via environmental collaboration
- **SDG 16**: Peace & Justice through transparent governance
- **SDG 17**: Partnerships for the Goals through global networking

## 🏆 Awards Consideration

Designed for **Awwwards "Best Social Good"** and **Webby "AI for Humanity"** recognition:

- **Profound Inclusivity**: 10+ language support, WCAG 3.0 compliance, offline access
- **Measurable Impact**: Unity Points system, global statistics, carbon offset tracking
- **Viral Potential**: Shareable impact GIFs, #SDGUnited hashtags, continental leaderboards

## 🔧 Development

### Project Structure
\`\`\`
src/
├── app/                 # Next.js App Router
├── components/          # React components
├── contexts/           # React contexts for state management
└── api/               # API routes for Grok integration
\`\`\`

### Key Components
- `GreenHorizenHUD`: Main overlay interface
- `WorldPulseOrb`: Particle visualization system
- `SDGQuest`: Quick-start challenge buttons
- `NetworkFeed`: Global insights sidebar
- `LangSelector`: Multilingual support

### Mock Data
All external APIs are mocked for demonstration:
- Green AI responses with SDG-aligned insights
- Global network feed with continental diversity
- Unity Points and quest completion tracking

## 📱 PWA Features

- **Offline Support**: Cached SDG primers and core functionality
- **Install Prompt**: Add to home screen on mobile devices
- **Background Sync**: Queue insights when offline
- **Push Notifications**: Global challenge alerts (optional)

## 🌍 Contributing to Global Unity

This open-source project welcomes contributions that advance global collective intelligence:

1. **Code Contributions**: Enhance accessibility, performance, or features
2. **Translation**: Add support for additional languages
3. **SDG Content**: Contribute region-specific sustainability insights
4. **Design**: Improve inclusivity and visual accessibility

## 📄 License

MIT License - Built for global adoption and maximum positive impact.

---

**"Green Horizen transcends screens, weaving AI into the fabric of global solidarity—uniting queries into quests that heal our world, one horizon at a time."**

Deploy this app to help connect and empower 7 billion voices for planetary progress. 🌍✨
