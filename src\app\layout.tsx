import type React from "react"
import type { <PERSON>ada<PERSON> } from "next"
import { Inter, Space_Grotesk } from "next/font/google"
import "./globals.css"
import { HorizonProvider } from "../contexts/HorizonContext"
import { IntlProvider } from "../contexts/IntlContext"

const inter = Inter({
  subsets: ["latin"],
  variable: "--font-inter",
})

const spaceGrotesk = Space_Grotesk({
  subsets: ["latin"],
  variable: "--font-space-grotesk",
})

export const metadata: Metadata = {
  title: "Grok Horizon - Global Collective Intelligence",
  description: "Revolutionary HUD web app harnessing Grok AI for global collective intelligence and social impact",
  keywords: ["AI", "Grok", "SDG", "Global", "Collective Intelligence", "Social Impact"],
  authors: [{ name: "Grok Horizon Team" }],
  openGraph: {
    title: "Grok Horizon - Global Collective Intelligence",
    description: "Join the global movement for collective intelligence and social impact",
    type: "website",
  },
  manifest: "/manifest.json",
  themeColor: "#10b981",
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="en" className={`${inter.variable} ${spaceGrotesk.variable}`}>
      <body className="font-inter antialiased bg-slate-900 text-white">
        <IntlProvider>
          <HorizonProvider>{children}</HorizonProvider>
        </IntlProvider>
      </body>
    </html>
  )
}
