"use client"

import { createContext, useContext, useState, useEffect, type ReactNode } from "react"

interface IntlContextType {
  currentLang: string
  setCurrentLang: (lang: string) => void
  translate: (key: string) => string
}

// Mock translations for demonstration
const TRANSLATIONS: Record<string, Record<string, string>> = {
  en: {
    "hud.title": "Grok Horizon",
    "query.placeholder": "Ask about global challenges, SDGs, or local solutions...",
    "query.error": "Unable to process query. Please try again.",
    "sdg.title": "SDG Quests",
    "sdg.subtitle": "Sustainable Development Goals",
    "network.title": "Global Network",
    "network.empty": "Join the conversation to see global insights",
  },
  es: {
    "hud.title": "Grok Horizonte",
    "query.placeholder": "Pregunta sobre desafíos globales, ODS o soluciones locales...",
    "query.error": "No se pudo procesar la consulta. Inténtalo de nuevo.",
    "sdg.title": "Misiones ODS",
    "sdg.subtitle": "Objetivos de Desarrollo Sostenible",
    "network.title": "Red Global",
    "network.empty": "Únete a la conversación para ver perspectivas globales",
  },
  bn: {
    "hud.title": "গ্রোক হরাইজন",
    "query.placeholder": "বৈশ্বিক চ্যালেঞ্জ, এসডিজি বা স্থানীয় সমাধান সম্পর্কে জিজ্ঞাসা করুন...",
    "query.error": "প্রশ্ন প্রক্রিয়া করতে অক্ষম। অনুগ্রহ করে আবার চেষ্টা করুন।",
    "sdg.title": "এসডিজি অভিযান",
    "sdg.subtitle": "টেকসই উন্নয়ন লক্ষ্য",
    "network.title": "বৈশ্বিক নেটওয়ার্ক",
    "network.empty": "বৈশ্বিক অন্তর্দৃষ্টি দেখতে কথোপকথনে যোগ দিন",
  },
  // Add more languages as needed
}

const IntlContext = createContext<IntlContextType | undefined>(undefined)

export function IntlProvider({ children }: { children: ReactNode }) {
  const [currentLang, setCurrentLang] = useState("en")

  // Auto-detect language on mount
  useEffect(() => {
    const savedLang = localStorage.getItem("horizon-language")
    const browserLang = navigator.language.split("-")[0]

    if (savedLang && TRANSLATIONS[savedLang]) {
      setCurrentLang(savedLang)
    } else if (TRANSLATIONS[browserLang]) {
      setCurrentLang(browserLang)
    }
  }, [])

  // Save language preference
  useEffect(() => {
    localStorage.setItem("horizon-language", currentLang)
  }, [currentLang])

  const translate = (key: string): string => {
    return TRANSLATIONS[currentLang]?.[key] || TRANSLATIONS.en[key] || key
  }

  return (
    <IntlContext.Provider
      value={{
        currentLang,
        setCurrentLang,
        translate,
      }}
    >
      {children}
    </IntlContext.Provider>
  )
}

export function useIntl() {
  const context = useContext(IntlContext)
  if (context === undefined) {
    throw new Error("useIntl must be used within an IntlProvider")
  }
  return context
}
