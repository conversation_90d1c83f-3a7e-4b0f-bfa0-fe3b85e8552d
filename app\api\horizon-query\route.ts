import { type NextRequest, NextResponse } from "next/server"

// <PERSON><PERSON> responses for different SDG categories
const MOCK_RESPONSES = {
  climate: [
    "In your region, community solar cooperatives can reduce emissions by 30%. Join 500+ global users exploring renewable energy solutions! 🌱",
    "Local tree-planting initiatives show 40% air quality improvement. Connect with 2.3K environmental advocates worldwide! 🌳",
    "Urban vertical gardens reduce building energy by 25%. Your query joins 800 voices in sustainable architecture! 🏢",
  ],
  health: [
    "Preventive health screenings reduce costs by 60%. Your community can join 1.2K health advocates globally! ❤️",
    "Mental health support networks show 45% improvement rates. Connect with 3.1K wellness champions worldwide! 🧠",
    "Community fitness programs boost longevity by 20%. Join 950 active health communities globally! 💪",
  ],
  education: [
    "Digital literacy programs increase opportunities by 70%. Your query connects with 2.8K education innovators! 📚",
    "Peer-to-peer learning networks show 55% skill improvement. Join 1.5K knowledge-sharing communities! 🎓",
    "Open-source educational resources reach 10M+ learners. Connect with 4.2K education advocates! 💡",
  ],
  water: [
    "Rainwater harvesting systems provide 40% water independence. Join 1.8K water conservation champions! 💧",
    "Smart irrigation reduces water usage by 50%. Your insight connects with 2.1K sustainable farming advocates! 🌾",
    "Community water purification projects serve 100K+ people. Join 3.5K clean water initiatives globally! 🚰",
  ],
  justice: [
    "Transparent governance platforms increase trust by 65%. Connect with 2.7K civic engagement advocates! ⚖️",
    "Community mediation programs reduce conflicts by 80%. Join 1.4K peace-building initiatives worldwide! 🕊️",
    "Digital rights education empowers 500K+ citizens. Your query joins 3.8K justice advocates globally! 📱",
  ],
  energy: [
    "Micro-grid systems provide 90% energy reliability. Connect with 2.2K renewable energy communities! ⚡",
    "Energy-efficient appliances reduce costs by 35%. Join 1.9K sustainable living advocates worldwide! 🔌",
    "Community energy storage increases resilience by 60%. Your insight connects with 2.6K energy innovators! 🔋",
  ],
  general: [
    "Global collaboration increases solution effectiveness by 85%. Your voice joins 47.2K active change-makers! 🌍",
    "Cross-cultural knowledge sharing accelerates progress by 120%. Connect with diverse global communities! 🤝",
    "Collective intelligence amplifies individual impact by 300%. Join the worldwide movement for positive change! 🚀",
  ],
}

const CONTINENTS = ["Africa", "Asia", "Europe", "North America", "South America", "Oceania"]

const GLOBAL_FEED_EXAMPLES = [
  { continent: "Africa", tip: "Solar-powered water pumps transforming rural communities", points: 25 },
  { continent: "Asia", tip: "Vertical farming feeding urban populations sustainably", points: 30 },
  { continent: "Europe", tip: "Circular economy reducing waste by 70%", points: 20 },
  { continent: "North America", tip: "Indigenous knowledge preserving biodiversity", points: 35 },
  { continent: "South America", tip: "Reforestation drones planting 1M trees monthly", points: 40 },
  { continent: "Oceania", tip: "Ocean cleanup technology removing plastic waste", points: 28 },
]

export async function POST(request: NextRequest) {
  try {
    const { query, lang, sdg } = await request.json()

    // Simulate processing delay
    await new Promise((resolve) => setTimeout(resolve, 1000 + Math.random() * 2000))

    // Determine response category based on query content
    let category = "general"
    const queryLower = query.toLowerCase()

    if (queryLower.includes("climate") || queryLower.includes("environment") || queryLower.includes("carbon")) {
      category = "climate"
    } else if (queryLower.includes("health") || queryLower.includes("medical") || queryLower.includes("wellness")) {
      category = "health"
    } else if (queryLower.includes("education") || queryLower.includes("learning") || queryLower.includes("school")) {
      category = "education"
    } else if (queryLower.includes("water") || queryLower.includes("irrigation") || queryLower.includes("drought")) {
      category = "water"
    } else if (queryLower.includes("justice") || queryLower.includes("governance") || queryLower.includes("rights")) {
      category = "justice"
    } else if (queryLower.includes("energy") || queryLower.includes("renewable") || queryLower.includes("solar")) {
      category = "energy"
    }

    // Select random response from category
    const responses = MOCK_RESPONSES[category as keyof typeof MOCK_RESPONSES] || MOCK_RESPONSES.general
    const response = responses[Math.floor(Math.random() * responses.length)]

    // Generate points based on query complexity and category
    const basePoints = 10
    const categoryBonus = category === "general" ? 5 : 15
    const lengthBonus = Math.min(Math.floor(query.length / 20), 10)
    const points = basePoints + categoryBonus + lengthBonus

    // Select random continent and global feed example
    const continent = CONTINENTS[Math.floor(Math.random() * CONTINENTS.length)]
    const globalFeed = GLOBAL_FEED_EXAMPLES[Math.floor(Math.random() * GLOBAL_FEED_EXAMPLES.length)]

    // Mock translation (in real app, would use proper translation service)
    let translatedResponse = response
    if (lang === "es") {
      // Simple mock Spanish translation
      translatedResponse = response
        .replace("community", "comunidad")
        .replace("global", "global")
        .replace("Join", "Únete")
        .replace("Connect", "Conecta")
    }

    return NextResponse.json({
      response: translatedResponse,
      points,
      continent,
      globalFeed,
      animation: Math.random() > 0.5 ? "unite" : "processing",
      metadata: {
        category,
        language: lang,
        processingTime: Math.round(1000 + Math.random() * 2000),
        carbonOffset: "0.1g CO2 - offset by planting virtual mangrove 🌱",
      },
    })
  } catch (error) {
    console.error("Horizon query error:", error)
    return NextResponse.json({ error: "Failed to process horizon query" }, { status: 500 })
  }
}
